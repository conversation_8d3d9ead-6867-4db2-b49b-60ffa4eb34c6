import os
import json
import asyncio
import httpx
import logging
import colorama
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from colorama import Fore, Style

# 初始化colorama
colorama.init(autoreset=True)

# 自定义彩色日志格式化器
class ColoredFormatter(logging.Formatter):
    COLORS = {
        'DEBUG': Fore.BLUE,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Style.BRIGHT
    }

    def format(self, record):
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{Style.RESET_ALL}"
            record.msg = f"{self.COLORS[levelname]}{record.msg}{Style.RESET_ALL}"
        return super().format(record)

# 配置日志
logger = logging.getLogger("summary_generator")
logger.setLevel(logging.INFO)

# 文件处理器
file_handler = logging.FileHandler("summary_generator.log",encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# 控制台处理器（带颜色）
console_handler = logging.StreamHandler()
console_handler.setFormatter(ColoredFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# 配置常量
SOURCE_DIR = "754163178123956224_NE40E V800R023C10SPC500 配置指南 基础配置"  # 源数据目录
TARGET_DIR = "754163178123956224_NE40E V800R023C10SPC500 配置指南 基础配置_question"  # 目标摘要目录
LLM_API_URL = "http://10.186.2.176:10010/CUCCAI-llm-hub/chat/completions"  # LLM API地址
API_KEY = "ead02826-eff0-43a6-9b1a-90c5e7022a3d"  # API密钥
MAX_CONCURRENT_REQUESTS = 5  # 最大并发请求数
MAX_RETRIES = 5  # 最大重试次数
RETRY_DELAY = 5  # 重试延迟（秒）
LLM_NAME="Qwen3-32B" # deepseek_v3_int8_vpc,Qwen3-32B
TASK_TYPE = "question"  # 任务类型：'summary' 或 'question'
PROXY = "socks5://192.168.0.105:44444"  # 代理地址

# 提示词模板
PROMPT_TEMPLATE = """
你是一个专业的文本摘要助手，你的任务是从我提供的文档中提取出一个简洁、准确的摘要。

请严格遵守以下规则：
1.  **摘要必须是独立成文的，能够让读者快速理解文档的核心内容，无需阅读全文。**
2.  **请返回三种不同格式的摘要，包括：描述性摘要，信息性摘要，结构化摘要 **
3.  **摘要长度控制在50至100字之间，并且经可能组织语言突出主题词。**
4.  **请保持客观，只总结原文内容，不要添加任何个人观点、评论或引申。**
5.  **摘要必须以纯文本形式返回，不要有任何多余的前缀、标题或结尾。**
6.  **将三种摘要的纯文本，放置在一个JSON中返回，{{DA:"描述性摘要",IA:"信息性摘要",SA:"结构化摘要"}}**

**文档内容:**
{content}

**示例:**
如果文档内容是"人工智能是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。它自诞生以来，已经发展出了机器学习、深度学习、自然语言处理等多个子领域。"
那么你应该返回：
{{
DA:"该文本介绍了人工智能（AI）的定义，将其描述为一门旨在模拟、扩展人类智能的新兴技术科学。文中提到了AI的几个主要子领域，包括机器学习、深度学习和自然语言处理。",
IA:"人工智能被定义为一门研究和开发模拟、延伸人类智能的理论、方法、技术及应用系统的新技术科学。自诞生以来，AI已发展出多个关键子领域，例如机器学习、深度学习和自然语言处理。",
SA:"**背景:** 人工智能（AI）是一门旨在模拟、延伸和扩展人类智能的新兴技术科学。**内容:** AI的研究和开发涵盖了理论、方法、技术和应用系统。其发展历程中已经形成了多个重要子领域。**子领域:** 目前，AI的主要子领域包括机器学习、深度学习和自然语言处理。"}}
"""


QA_PROMPT_TEMPLATE = """你是一位经验丰富的技术文档分析师和测试人员。请根据我提供的技术文档，从以下五个角度设计高质量的问题。所有问题都必须能够直接或间接在文档中找到明确答案。

请将你生成的问题以 JSON 格式 输出。JSON对象应包含五个键，每个键对应一个问题类型，其值是一个包含该类型问题的字符串数组。

问题设计角度及对应的键名：
- 定义与概念验证 (DefinitionsAndConcepts): 验证核心概念和术语的理解。
- 步骤与操作复现 (StepsAndOperations): 测试能否根据文档复现操作。
- 配置与参数查询 (ConfigurationAndParameters): 查找配置项和参数值。
- 错误与解决方案溯源 (ErrorsAndSolutions): 定位和解决文档中提到的问题。
- 组件与依赖关系 (ComponentsAndDependencies): 理解系统架构和依赖关系。

请严格遵守以下规则：
1. **请保持客观，只提问原文内容,确保问题的答案在原文中可以被找到，不要添加任何个人观点、评论或引申。**
2. **问题必须以纯文本形式返回，不要有任何多余的前缀、标题或结尾,不要有多余的标签，思考过程，例如<think>等。**
3. **输出必须是有效的JSON格式，包含上述五个键，每个键对应一个问题数组,每个数组中2个问题。2个问题的类型分别为"是非问"和"特指问"**

4. **直接输出JSON，不要有任何其他无关内容

输出格式示例：
{{
  "DefinitionsAndConcepts": [
    "某概念的官方定义是什么？",
    "如何解释某术语的核心含义？"
  ],
  "StepsAndOperations": [
    "执行某操作的具体步骤是什么？",
    "如何配置某功能？"
  ],
  "ConfigurationAndParameters": [
    "某参数的默认值是多少？",
    "如何设置某配置项？"
  ],
  "ErrorsAndSolutions": [
    "遇到某错误时如何解决？",
    "某问题的排查方法是什么？"
  ],
  "ComponentsAndDependencies": [
    "某组件依赖哪些其他组件？",
    "系统架构中包含哪些模块？"
  ]
}}

文档内容：
{content}"""


async def get_all_txt_files(directory: str) -> List[str]:
    """
    递归获取目录下所有的txt文件路径
    """
    txt_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".txt"):
                txt_files.append(os.path.join(root, file))
    return txt_files


async def read_txt_file(file_path: str) -> str:
    """
    读取txt文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # 尝试使用其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.read()
        except Exception as e:
            logger.error(f"无法读取文件 {file_path}: {e}")
            return ""
    except Exception as e:
        logger.error(f"读取文件 {file_path} 时出错: {e}")
        return ""


def parse_llm_json_response(content: str, expected_schema: Dict) -> tuple[Optional[Dict], bool]:
    """
    通用的LLM JSON响应解析器

    Args:
        content: LLM返回的原始文本内容
        expected_schema: 期望的JSON结构，包含默认值

    Returns:
        tuple: (解析后的JSON字典, 是否解析成功)
        - 如果解析成功，返回 (parsed_json, True)
        - 如果解析失败，返回 (None, False)
    """

    import re

    pattern = r"<think>.*?</think>"
    content=re.sub(pattern, "", content, flags=re.DOTALL)

    try:
        # 方法1: 尝试直接解析整个内容为JSON
        try:
            parsed = json.loads(content.strip())
            if validate_json_schema(parsed, expected_schema):
                return parsed, True
        except json.JSONDecodeError:
            pass

        # 方法2: 查找JSON代码块（```json ... ```）
        json_block_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', content, re.DOTALL)
        if json_block_match:
            try:
                parsed = json.loads(json_block_match.group(1))
                if validate_json_schema(parsed, expected_schema):
                    return parsed, True
            except json.JSONDecodeError:
                pass

        # 方法3: 查找最大的JSON对象
        # 使用更宽松的正则表达式匹配JSON对象
        json_matches = re.finditer(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content, re.DOTALL)

        for match in json_matches:
            json_str = match.group(0)
            try:
                # 尝试修复常见的JSON格式问题
                json_str = fix_json_format(json_str)
                parsed = json.loads(json_str)

                # 验证解析结果是否包含期望的键
                if validate_json_schema(parsed, expected_schema):
                    return parsed, True
            except json.JSONDecodeError:
                continue

        # 所有解析方法都失败
        logger.warning(f"无法解析JSON内容，所有解析方法都失败")
        logger.error(f"原始内容: {content}")

        return None, False

    except Exception as e:
        logger.error(f"JSON解析过程中出现异常: {e}")
        return None, False


def fix_json_format(json_str: str) -> str:
    """
    修复常见的JSON格式问题

    Args:
        json_str: 原始JSON字符串

    Returns:
        修复后的JSON字符串
    """
    import re

    # 移除多余的空白字符
    json_str = json_str.strip()

    # 修复键名缺少引号的问题
    # 匹配 key: 或 key : 格式，确保键名有双引号
    json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

    # 修复单引号为双引号
    json_str = json_str.replace("'", '"')

    # 修复尾随逗号问题
    json_str = re.sub(r',\s*}', '}', json_str)
    json_str = re.sub(r',\s*]', ']', json_str)

    # 确保字符串值被正确引用
    # 这个比较复杂，暂时跳过，依赖LLM生成相对正确的格式

    return json_str


def validate_json_schema(parsed_json: Dict, expected_schema: Dict) -> bool:
    """
    验证解析的JSON是否符合期望的结构

    Args:
        parsed_json: 解析后的JSON对象
        expected_schema: 期望的JSON结构

    Returns:
        是否符合期望结构
    """
    if not isinstance(parsed_json, dict):
        return False

    # 检查是否包含期望的键
    expected_keys = set(expected_schema.keys())
    actual_keys = set(parsed_json.keys())

    # 至少包含一半的期望键才认为是有效的
    overlap = len(expected_keys.intersection(actual_keys))
    return overlap >= len(expected_keys) * 0.5


def create_error_response(expected_schema: Dict, error_message: str) -> Dict:
    """
    创建错误响应，保持与期望结构一致

    Args:
        expected_schema: 期望的JSON结构
        error_message: 错误信息

    Returns:
        包含错误信息的响应字典
    """
    error_response = {}
    for key, default_value in expected_schema.items():
        if isinstance(default_value, list):
            error_response[key] = [error_message]
        else:
            error_response[key] = error_message

    return error_response


async def generate_llm_response(content: str, task_type: str = "question", retry_count: int = 0, json_retry_count: int = 0) -> Optional[Dict]:
    """
    通用的LLM响应生成函数，支持多种任务类型和JSON解析失败重试

    Args:
        content: 输入文档内容
        task_type: 任务类型，"summary" 或 "question"
        retry_count: API请求重试次数
        json_retry_count: JSON解析失败重试次数

    Returns:
        解析后的JSON响应
    """
    if not content.strip():
        logger.warning("内容为空，跳过处理")
        return None

    # 根据任务类型选择提示词模板和期望结构
    if task_type == "summary":
        prompt_template = PROMPT_TEMPLATE
        expected_schema = {
            "DA": "默认描述性摘要",
            "IA": "默认信息性摘要",
            "SA": "默认结构化摘要"
        }
        system_message = "你是一个专业的文本摘要助手。"
    elif task_type == "question":
        prompt_template = QA_PROMPT_TEMPLATE
        expected_schema = {
            "DefinitionsAndConcepts": [],
            "StepsAndOperations": [],
            "ConfigurationAndParameters": [],
            "ErrorsAndSolutions": [],
            "ComponentsAndDependencies": []
        }
        system_message = "你是一位经验丰富的技术文档分析师和测试人员。"
    else:
        logger.error(f"不支持的任务类型: {task_type}")
        return None

    # 构建请求数据
    request_data = {
        "model": LLM_NAME,
        "messages": [
            {"role": "system", "content": system_message},
            {"role": "user", "content": prompt_template.format(content=content)}
        ],
        "stream": False,  # 设置为False以获取完整响应
        "temperature": 0.1,
        "max_tokens": 4000
    }

    try:
        transport = httpx.AsyncHTTPTransport(proxy=PROXY)
        async with httpx.AsyncClient(transport=transport,timeout=60.0) as client:
            response = await client.post(
                LLM_API_URL,
                headers={
                    'Authorization': f'Bearer {API_KEY}',
                    'Content-Type': 'application/json'
                },
                json=request_data
            )

            if response.status_code == 200:
                result = response.json()
                # 提取LLM返回的内容
                if 'choices' in result and len(result['choices']) > 0:
                    response_content = result['choices'][0]['message']['content']

                    # 使用通用JSON解析器
                    parsed_result, parse_success = parse_llm_json_response(response_content, expected_schema)

                    if parse_success:
                        logger.info(f"JSON解析成功")
                        return parsed_result
                    else:
                        # JSON解析失败，检查是否需要重试
                        if json_retry_count < MAX_RETRIES:
                            json_retry_count += 1
                            logger.warning(f"JSON解析失败，重新请求LLM生成 (JSON重试 {json_retry_count}/{MAX_RETRIES})")
                            await asyncio.sleep(2)  # 短暂等待后重试
                            return await generate_llm_response(content, task_type, 0, json_retry_count)
                        else:
                            logger.error(f"JSON解析失败次数过多，返回错误响应")
                            return create_error_response(expected_schema, "JSON解析失败次数过多")
                return None
            elif response.status_code == 429 and retry_count < MAX_RETRIES:
                # 请求过于频繁，触发熔断，进行重试
                retry_count += 1
                wait_time = RETRY_DELAY * retry_count
                logger.warning(f"请求过于频繁，等待 {wait_time} 秒后重试 (API重试 {retry_count}/{MAX_RETRIES})")
                await asyncio.sleep(wait_time)
                return await generate_llm_response(content, task_type, retry_count, json_retry_count)
            else:
                logger.error(f"LLM API请求失败: {response.status_code} - {response.text}")
                if retry_count < MAX_RETRIES:
                    retry_count += 1
                    wait_time = RETRY_DELAY * retry_count
                    logger.warning(f"等待 {wait_time} 秒后重试 (API重试 {retry_count}/{MAX_RETRIES})")
                    await asyncio.sleep(wait_time)
                    return await generate_llm_response(content, task_type, retry_count, json_retry_count)
                return None
    except Exception as e:
        logger.error(f"生成{task_type}时出错: {e}")
        if retry_count < MAX_RETRIES:
            retry_count += 1
            wait_time = RETRY_DELAY * retry_count
            logger.warning(f"等待 {wait_time} 秒后重试 (API重试 {retry_count}/{MAX_RETRIES})")
            await asyncio.sleep(wait_time)
            return await generate_llm_response(content, task_type, retry_count, json_retry_count)
        logger.error('生成失败')
        return None


async def generate_summary(content: str, retry_count: int = 0) -> Optional[Dict]:
    """
    生成摘要的便捷函数（保持向后兼容）
    """
    return await generate_llm_response(content, "summary", retry_count)


async def generate_questions(content: str, retry_count: int = 0) -> Optional[Dict]:
    """
    生成问题的便捷函数
    """
    return await generate_llm_response(content, "question", retry_count)


async def save_summary(source_path: str, summary: Dict) -> None:
    """
    保存摘要到目标目录，保持相同的目录结构，使用.json扩展名
    """
    if not summary:
        logger.warning(f"摘要为空，跳过保存: {source_path}")
        return

    try:
        # 计算目标路径
        rel_path = os.path.relpath(source_path, SOURCE_DIR)
        # 将.txt扩展名替换为.json
        base_path, _ = os.path.splitext(rel_path)
        json_path = f"{base_path}.json"
        target_path = os.path.join(TARGET_DIR, json_path)
        
        # 确保目标目录存在（使用pathlib创建完整的目录结构）
        target_dir = os.path.dirname(target_path)
        Path(target_dir).mkdir(parents=True, exist_ok=True)
        
        # 检查目录是否成功创建
        if not os.path.exists(target_dir):
            logger.error(f"无法创建目录: {target_dir}")
            return
        
        # 将摘要写入JSON文件
        with open(target_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        logger.info(f"结果文件已保存到: {target_path}")
    except Exception as e:
        logger.error(f"保存文件时出错: {e}, 源文件: {source_path}")
        # 记录更详细的错误信息
        import traceback
        logger.debug(f"错误详情: {traceback.format_exc()}")



async def process_file(file_path: str, semaphore: asyncio.Semaphore, task_type: Optional[str] = None) -> None:
    """
    处理单个文件：读取内容，生成响应，保存结果

    Args:
        file_path: 文件路径
        semaphore: 并发控制信号量
        task_type: 任务类型，如果为None则使用全局配置
    """
    async with semaphore:
        current_task_type = task_type or TASK_TYPE
        logger.info(f"处理文件: {file_path} (任务类型: {current_task_type})")
        content = await read_txt_file(file_path)
        if content:
            result = await generate_llm_response(content, current_task_type)
            if result:
                await save_summary(file_path, result)
        else:
            logger.warning(f"文件内容为空，跳过: {file_path}")


async def create_directory_structure():
    """
    在目标目录中创建与源目录相同的目录结构
    """
    logger.info(f"开始创建目录结构: 从 {SOURCE_DIR} 到 {TARGET_DIR}")
    # 确保目标目录存在
    os.makedirs(TARGET_DIR, exist_ok=True)
    
    # 遍历源目录中的所有子目录
    for root, dirs, _ in os.walk(SOURCE_DIR):
        for dir_name in dirs:
            # 计算相对路径
            source_dir = os.path.join(root, dir_name)
            rel_dir = os.path.relpath(source_dir, SOURCE_DIR)
            target_dir = os.path.join(TARGET_DIR, rel_dir)
            
            # 创建目标目录
            os.makedirs(target_dir, exist_ok=True)
            logger.debug(f"创建目录: {target_dir}")
    
    logger.info(f"目录结构创建完成")

async def main(task_type: Optional[str] = None):
    """
    主处理函数

    Args:
        task_type: 任务类型，如果为None则使用全局配置
    """
    current_task_type = task_type or TASK_TYPE

    # 首先创建完整的目录结构
    await create_directory_structure()

    # 获取所有txt文件
    logger.info(f"开始扫描目录: {SOURCE_DIR}")
    txt_files = await get_all_txt_files(SOURCE_DIR)
    logger.info(f"找到 {len(txt_files)} 个txt文件")
    logger.info(f"当前任务类型: {current_task_type}")

    # 创建信号量控制并发
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

    # 创建任务列表
    tasks = [process_file(file_path, semaphore, current_task_type) for file_path in txt_files]


    # 执行所有任务
    logger.info("开始处理文件...")

    await asyncio.gather(*tasks)
    logger.info("所有文件处理完成")


if __name__ == "__main__":
    asyncio.run(main())