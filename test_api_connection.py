#!/usr/bin/env python3
"""
测试私域OpenAI API连接

这个脚本用于测试私域OpenAI格式API的连接性和基本功能。
"""

import asyncio
import json
import httpx
import time


async def test_api_connection():
    """
    测试API连接
    """
    print("🔍 测试私域OpenAI API连接...")
    
    # API配置
    api_key = "618149eb-d43e-4ddc-b406-b0c0e1efd281"
    base_url = "http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions"
    model_name = "deepseek_v3_int8_vpc"
    
    print(f"API地址: {base_url}")
    print(f"模型: {model_name}")
    print(f"API Key: {api_key[:20]}...")
    print("-" * 50)
    
    # 构建测试请求
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "model": model_name,
        "messages": [
            {
                "role": "system", 
                "content": "你是一个有用的助手。请简洁地回答问题。"
            },
            {
                "role": "user", 
                "content": "请说'Hello, World!'并解释BGP是什么。"
            }
        ],
        "stream": False,
        "temperature": 0.5,
        "max_tokens": 500
    }
    
    try:
        print("📡 发送测试请求...")
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                base_url,
                headers=headers,
                json=payload
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  响应时间: {response_time:.2f}秒")
            print(f"📊 HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ API连接成功!")
                
                try:
                    result = response.json()
                    print("\n📄 响应结构:")
                    print(f"  - 包含 'choices': {'choices' in result}")
                    print(f"  - 包含 'usage': {'usage' in result}")
                    print(f"  - 包含 'model': {'model' in result}")
                    
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content']
                        print(f"\n💬 模型响应:")
                        print(f"  {content}")
                        
                        # 测试JSON解析能力
                        await test_json_extraction(api_key, base_url, model_name)
                        
                    else:
                        print("❌ 响应格式异常: 没有找到choices字段")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response.text}")
                    
            else:
                print(f"❌ API请求失败!")
                print(f"状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except httpx.TimeoutException:
        print("❌ 请求超时! API可能不可用或响应太慢。")
    except httpx.ConnectError:
        print("❌ 连接失败! 请检查API地址是否正确，服务是否运行。")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


async def test_json_extraction(api_key: str, base_url: str, model_name: str):
    """
    测试JSON格式的信息提取
    """
    print("\n🧪 测试JSON格式信息提取...")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # 测试简单的信息提取
    system_prompt = """你是一个信息提取助手。请从用户提供的文本中提取信息，并按照以下JSON格式返回：

{
    "extractions": [
        {
            "text": "提取的文本片段",
            "type": "信息类型",
            "value": "提取的值"
        }
    ]
}

请确保返回有效的JSON格式。"""

    user_prompt = """请从以下文本中提取人名和年龄信息：

李明是一名25岁的程序员，住在上海。"""

    payload = {
        "model": model_name,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "stream": False,
        "temperature": 0.3,
        "max_tokens": 300
    }
    
    try:
        async with httpx.AsyncClient(timeout=20.0) as client:
            response = await client.post(
                base_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"📝 原始响应:")
                    print(f"  {content}")
                    
                    # 尝试解析JSON
                    try:
                        # 清理响应
                        cleaned_content = content.strip()
                        if cleaned_content.startswith("```json"):
                            cleaned_content = cleaned_content[7:]
                        if cleaned_content.endswith("```"):
                            cleaned_content = cleaned_content[:-3]
                        cleaned_content = cleaned_content.strip()
                        
                        extracted_data = json.loads(cleaned_content)
                        print("✅ JSON解析成功!")
                        print("📊 提取结果:")
                        print(json.dumps(extracted_data, ensure_ascii=False, indent=2))
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print("💡 建议: 可能需要调整提示词以获得更好的JSON格式")
                        
            else:
                print(f"❌ JSON提取测试失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ JSON提取测试出错: {e}")


async def test_bgp_example():
    """
    测试BGP配置提取示例
    """
    print("\n🌐 测试BGP配置提取...")
    
    from langextract_simple_demo import PrivateOpenAIExtractor
    
    extractor = PrivateOpenAIExtractor()
    
    text = """
    router bgp 65001
     bgp router-id 192.168.1.1
     neighbor 10.0.0.2 remote-as 65002
    """
    
    task = "从BGP配置中提取AS号和路由器ID。"
    
    try:
        result = await extractor.extract_info(text, task)
        
        if result["success"]:
            print("✅ BGP配置提取成功!")
            print(json.dumps(result["data"], ensure_ascii=False, indent=2))
        else:
            print(f"❌ BGP配置提取失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ BGP测试出错: {e}")


async def main():
    """
    主测试函数
    """
    print("🚀 私域OpenAI API 连接测试")
    print("=" * 60)
    
    # 基本连接测试
    await test_api_connection()
    
    # BGP示例测试
    await test_bgp_example()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成!")
    print("\n💡 使用建议:")
    print("  1. 如果连接失败，请检查API服务是否运行")
    print("  2. 如果JSON解析失败，可以调整提示词")
    print("  3. 如果响应慢，可以减少max_tokens参数")
    print("  4. 建议在生产环境中添加重试机制")


if __name__ == "__main__":
    asyncio.run(main())
