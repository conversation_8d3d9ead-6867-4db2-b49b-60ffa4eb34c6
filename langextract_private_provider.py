#!/usr/bin/env python3
"""
LangExtract 私域OpenAI Provider

这是一个真正的langextract自定义provider，用于连接私域OpenAI格式的API。
API地址: http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions
模型: deepseek_v3_int8_vpc

这个实现继承自langextract.inference.BaseLanguageModel，
可以无缝集成到langextract的工作流中。
"""

import dataclasses
import json
from typing import Any, Dict, Iterator, Optional, Sequence
import httpx
import langextract as lx


@lx.providers.registry.register(
    r'^deepseek',  # 匹配以deepseek开头的模型ID
)
@dataclasses.dataclass(init=False)
class PrivateOpenAIProvider(lx.inference.BaseLanguageModel):
    """
    私域OpenAI格式API的LangExtract Provider
    
    这是一个真正的langextract provider，继承自BaseLanguageModel，
    可以无缝集成到langextract的工作流中。
    """
    
    model_id: str
    api_key: str
    base_url: str
    temperature: float
    max_tokens: int
    response_schema: Optional[Dict[str, Any]] = None
    enable_structured_output: bool = False
    
    def __init__(
        self,
        model_id: str = "deepseek_v3_int8_vpc",
        api_key: str = "618149eb-d43e-4ddc-b406-b0c0e1efd281",
        base_url: str = "http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions",
        temperature: float = 0.5,
        max_tokens: int = 4000,
        **kwargs: Any,
    ):
        """
        初始化私域OpenAI Provider
        
        Args:
            model_id: 模型ID
            api_key: API密钥
            base_url: API基础URL
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数（包括schema相关参数）
        """
        super().__init__()
        
        self.model_id = model_id
        self.api_key = api_key
        self.base_url = base_url
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        # Schema相关参数（来自自定义schema的to_provider_config()）
        self.response_schema = kwargs.get('response_schema')
        self.enable_structured_output = kwargs.get('enable_structured_output', False)
        
        # 存储额外的kwargs
        self._extra_kwargs = kwargs
    
    def apply_schema(self, schema_instance: Optional[lx.schema.BaseSchema]) -> None:
        """
        应用或清除schema配置
        
        Args:
            schema_instance: 要应用的schema实例，或None来清除现有schema
        """
        super().apply_schema(schema_instance)
        
        if schema_instance:
            # 应用新的schema配置
            config = schema_instance.to_provider_config()
            self.response_schema = config.get('response_schema')
            self.enable_structured_output = config.get('enable_structured_output', False)
        else:
            # 清除schema配置
            self.response_schema = None
            self.enable_structured_output = False
    
    def infer(
        self, 
        batch_prompts: Sequence[str], 
        **kwargs: Any
    ) -> Iterator[Sequence[lx.inference.ScoredOutput]]:
        """
        对一批提示词运行推理
        
        Args:
            batch_prompts: 输入提示词
            **kwargs: 额外的生成参数
            
        Yields:
            ScoredOutput列表，每个提示词一个
        """
        # 构建请求配置
        config = {
            'temperature': kwargs.get('temperature', self.temperature),
            'max_tokens': kwargs.get('max_tokens', self.max_tokens),
        }
        
        # 处理每个提示词
        for prompt in batch_prompts:
            try:
                output = self._call_api(prompt, config)
                yield [lx.inference.ScoredOutput(score=1.0, output=output)]
            except Exception as e:
                raise lx.exceptions.InferenceRuntimeError(
                    f'API调用失败: {str(e)}', original=e
                ) from e
    
    def _call_api(self, prompt: str, config: Dict[str, Any]) -> str:
        """
        调用私域OpenAI API
        
        Args:
            prompt: 提示词
            config: 配置参数
            
        Returns:
            API响应内容
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # 构建消息
        messages = [{"role": "user", "content": prompt}]
        
        payload = {
            "model": self.model_id,
            "messages": messages,
            "stream": False,
            "temperature": config.get('temperature', self.temperature),
            "max_tokens": config.get('max_tokens', self.max_tokens)
        }
        
        # 如果启用了结构化输出，可以在这里添加相关配置
        if self.enable_structured_output and self.response_schema:
            # 注意：这里的实现取决于你的私域API是否支持结构化输出
            # 如果不支持，langextract会在后处理中解析JSON
            pass
        
        # 发送请求
        with httpx.Client(timeout=60.0) as client:
            try:
                response = client.post(
                    self.base_url,
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                
                # 解析OpenAI格式的响应
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    raise ValueError(f"意外的响应格式: {result}")
                    
            except httpx.HTTPError as e:
                raise Exception(f"HTTP请求失败: {e}")
            except json.JSONDecodeError as e:
                raise Exception(f"JSON解析失败: {e}")


# 确保provider被注册
def ensure_provider_registered():
    """确保provider已注册到langextract"""
    # 触发provider加载
    lx.providers.load_plugins_once()
    
    # 检查是否注册成功
    entries = lx.providers.registry.list_entries()
    for entry in entries:
        if 'PrivateOpenAI' in str(entry) or 'deepseek' in str(entry):
            print(f"✅ Provider已注册: {entry}")
            return True
    
    print("❌ Provider注册失败")
    return False


if __name__ == "__main__":
    # 测试provider注册
    print("测试私域OpenAI Provider注册...")
    ensure_provider_registered()
