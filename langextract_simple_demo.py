#!/usr/bin/env python3
"""
LangExtract 简单演示 - 使用私域OpenAI格式模型

这是一个简化版的demo，展示如何使用私域OpenAI格式的API进行文本信息提取。

使用方法：
1. 确保私域API服务正在运行
2. 运行: uv run python langextract_simple_demo.py
3. 查看提取结果

API配置：
- 地址: http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions
- 模型: deepseek_v3_int8_vpc
- API Key: 618149eb-d43e-4ddc-b406-b0c0e1efd281
"""

import asyncio
import json
from typing import Dict, List
import httpx


class PrivateOpenAIExtractor:
    """
    私域OpenAI API信息提取器
    """
    
    def __init__(
        self,
        api_key: str = "618149eb-d43e-4ddc-b406-b0c0e1efd281",
        base_url: str = "http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions",
        model_name: str = "deepseek_v3_int8_vpc"
    ):
        """
        初始化提取器
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
    
    async def extract_info(self, text: str, extraction_task: str) -> Dict:
        """
        从文本中提取信息
        
        Args:
            text: 要提取信息的文本
            extraction_task: 提取任务描述
            
        Returns:
            提取结果字典
        """
        # 构建提示词
        system_prompt = f"""你是一个专业的信息提取助手。{extraction_task}

请按照以下JSON格式返回提取结果：
{{
    "extractions": [
        {{
            "text": "提取的文本片段",
            "type": "信息类型",
            "value": "提取的值",
            "attributes": {{
                "key": "value"
            }}
        }}
    ]
}}

请确保返回有效的JSON格式，不要包含任何其他文本。"""

        user_prompt = f"请从以下文本中提取信息：\n\n{text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "stream": False,
            "temperature": 0.3,
            "max_tokens": 2000
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.base_url,
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                
                # 解析OpenAI格式的响应
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    
                    # 尝试解析JSON响应
                    try:
                        # 清理响应文本
                        cleaned_content = content.strip()
                        if cleaned_content.startswith("```json"):
                            cleaned_content = cleaned_content[7:]
                        if cleaned_content.endswith("```"):
                            cleaned_content = cleaned_content[:-3]
                        cleaned_content = cleaned_content.strip()
                        
                        extracted_data = json.loads(cleaned_content)
                        return {
                            "success": True,
                            "data": extracted_data,
                            "raw_response": content
                        }
                    except json.JSONDecodeError as e:
                        return {
                            "success": False,
                            "error": f"JSON解析失败: {e}",
                            "raw_response": content
                        }
                else:
                    return {
                        "success": False,
                        "error": f"意外的响应格式: {result}"
                    }
                    
        except httpx.HTTPError as e:
            return {
                "success": False,
                "error": f"HTTP请求失败: {e}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"未知错误: {e}"
            }


async def demo_person_info_extraction():
    """
    人员信息提取演示
    """
    print("=== 人员信息提取演示 ===")
    
    extractor = PrivateOpenAIExtractor()
    
    text = """
    张三是一名软件工程师，今年28岁，住在北京市朝阳区。
    他在阿里巴巴公司工作，月薪25000元。
    他的邮箱是********************，手机号是13812345678。
    """
    
    task = "从文本中提取人员的基本信息，包括姓名、年龄、居住地、工作公司、薪资、邮箱、手机号等。"
    
    result = await extractor.extract_info(text, task)
    
    if result["success"]:
        print("✅ 提取成功!")
        print("提取结果:")
        print(json.dumps(result["data"], ensure_ascii=False, indent=2))
    else:
        print("❌ 提取失败!")
        print(f"错误: {result['error']}")
        if "raw_response" in result:
            print(f"原始响应: {result['raw_response']}")
    
    return result


async def demo_bgp_config_extraction():
    """
    BGP配置信息提取演示
    """
    print("\n=== BGP配置信息提取演示 ===")
    
    extractor = PrivateOpenAIExtractor()
    
    text = """
    router bgp 65001
     bgp router-id ***********
     neighbor ******** remote-as 65002
     neighbor ******** description "ISP-A"
     neighbor ********** remote-as 65001
     network *********** mask ***********
    """
    
    task = "从BGP配置中提取关键信息，包括本地AS号、路由器ID、邻居配置、网络宣告等。"
    
    result = await extractor.extract_info(text, task)
    
    if result["success"]:
        print("✅ 提取成功!")
        print("提取结果:")
        print(json.dumps(result["data"], ensure_ascii=False, indent=2))
    else:
        print("❌ 提取失败!")
        print(f"错误: {result['error']}")
        if "raw_response" in result:
            print(f"原始响应: {result['raw_response']}")
    
    return result


async def demo_tech_stack_extraction():
    """
    技术栈信息提取演示
    """
    print("\n=== 技术栈信息提取演示 ===")
    
    extractor = PrivateOpenAIExtractor()
    
    text = """
    前端技术栈：React 18.2.0, TypeScript 4.9.5, Ant Design 5.1.2
    后端技术栈：Spring Boot 2.7.8, Java 17, MySQL 8.0.32
    部署环境：阿里云ECS，4核8G，CentOS 7.9
    """
    
    task = "从技术文档中提取技术栈信息，包括前端技术、后端技术、部署环境等，并识别技术名称和版本号。"
    
    result = await extractor.extract_info(text, task)
    
    if result["success"]:
        print("✅ 提取成功!")
        print("提取结果:")
        print(json.dumps(result["data"], ensure_ascii=False, indent=2))
    else:
        print("❌ 提取失败!")
        print(f"错误: {result['error']}")
        if "raw_response" in result:
            print(f"原始响应: {result['raw_response']}")
    
    return result


async def main():
    """
    主函数 - 运行所有演示
    """
    print("🚀 LangExtract 私域OpenAI API 演示")
    print("=" * 50)
    print("API地址: http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions")
    print("模型: deepseek_v3_int8_vpc")
    print("=" * 50)
    
    # 运行所有演示
    results = []
    
    try:
        result1 = await demo_person_info_extraction()
        results.append(("人员信息提取", result1))
        
        result2 = await demo_bgp_config_extraction()
        results.append(("BGP配置提取", result2))
        
        result3 = await demo_tech_stack_extraction()
        results.append(("技术栈提取", result3))
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 演示结果总结:")
    success_count = 0
    for name, result in results:
        status = "✅ 成功" if result.get("success", False) else "❌ 失败"
        print(f"  {name}: {status}")
        if result.get("success", False):
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个演示成功")
    print("=" * 50)


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
