#!/usr/bin/env python
"""
测试JSON解析失败重试逻辑

这个脚本测试当JSON解析失败时，系统是否会重新请求LLM生成内容。
"""

import asyncio
import json
from unittest.mock import patch, AsyncMock
from summary_generator import (
    generate_llm_response, 
    parse_llm_json_response,
    create_error_response
)

# 模拟不同的LLM响应
MOCK_RESPONSES = [
    # 第一次响应：格式错误的JSON
    """
    这是一个格式错误的响应，不包含有效的JSON：
    
    根据文档内容，我生成了以下摘要：
    DA: "这是描述性摘要"
    IA: "这是信息性摘要"
    SA: "这是结构化摘要"
    
    但是这个格式不是有效的JSON。
    """,
    
    # 第二次响应：正确的JSON格式
    """
    {
      "DA": "这是一个正确格式的描述性摘要，介绍了文档的主要内容。",
      "IA": "这是一个正确格式的信息性摘要，提供了关键信息。",
      "SA": "这是一个正确格式的结构化摘要，按照特定格式组织内容。"
    }
    """,
    
    # 第三次响应：部分正确的JSON（用于问题生成测试）
    """
    {
      "DefinitionsAndConcepts": [
        "什么是vLLM？",
        "分页注意力机制的定义是什么？"
      ],
      "StepsAndOperations": [
        "如何安装vLLM？",
        "启动服务的步骤是什么？"
      ],
      "ConfigurationAndParameters": [
        "默认端口是多少？"
      ],
      "ErrorsAndSolutions": [
        "如何解决内存不足问题？"
      ],
      "ComponentsAndDependencies": [
        "vLLM依赖哪些组件？"
      ]
    }
    """
]

class MockResponse:
    """模拟HTTP响应"""
    def __init__(self, status_code=200, json_data=None):
        self.status_code = status_code
        self._json_data = json_data or {}
    
    def json(self):
        return self._json_data

async def test_json_retry_logic():
    """测试JSON解析失败重试逻辑"""
    print("=== 测试JSON解析失败重试逻辑 ===")
    
    # 测试内容
    test_content = "这是一个测试文档，用于验证摘要生成功能。"
    
    # 模拟响应计数器
    response_counter = 0
    
    def mock_post(*args, **kwargs):
        """模拟HTTP POST请求"""
        nonlocal response_counter
        
        # 根据计数器返回不同的响应
        if response_counter < len(MOCK_RESPONSES):
            content = MOCK_RESPONSES[response_counter]
            response_counter += 1
        else:
            # 如果超出预定义响应，返回最后一个正确的响应
            content = MOCK_RESPONSES[-1]
        
        mock_response = MockResponse(200, {
            'choices': [{
                'message': {
                    'content': content
                }
            }]
        })
        
        return mock_response
    
    # 使用mock替换HTTP客户端
    with patch('httpx.AsyncClient') as mock_client:
        mock_instance = AsyncMock()
        mock_instance.post = AsyncMock(side_effect=mock_post)
        mock_client.return_value.__aenter__.return_value = mock_instance
        
        print("测试摘要生成重试逻辑...")
        
        # 测试摘要生成
        result = await generate_llm_response(test_content, "summary")
        
        print(f"重试后的结果: {result}")
        print(f"总共调用了 {response_counter} 次LLM API")
        
        if result and 'DA' in result and 'IA' in result and 'SA' in result:
            print("✓ 摘要生成重试逻辑测试通过")
        else:
            print("✗ 摘要生成重试逻辑测试失败")
        
        # 重置计数器测试问题生成
        response_counter = 0
        
        print("\n测试问题生成重试逻辑...")
        
        # 测试问题生成
        result = await generate_llm_response(test_content, "question")
        
        print(f"重试后的结果键: {list(result.keys()) if result else 'None'}")
        print(f"总共调用了 {response_counter} 次LLM API")
        
        if result and len(result.keys()) == 5:
            print("✓ 问题生成重试逻辑测试通过")
        else:
            print("✗ 问题生成重试逻辑测试失败")

async def test_parse_function_directly():
    """直接测试解析函数的返回值"""
    print("\n=== 测试解析函数返回值 ===")
    
    # 测试成功解析
    good_json = '{"DA": "测试摘要", "IA": "测试信息", "SA": "测试结构"}'
    expected_schema = {"DA": "", "IA": "", "SA": ""}
    
    result, success = parse_llm_json_response(good_json, expected_schema)
    print(f"正确JSON解析: 成功={success}, 结果={result}")
    
    # 测试失败解析
    bad_json = "这不是JSON格式的内容"
    result, success = parse_llm_json_response(bad_json, expected_schema)
    print(f"错误JSON解析: 成功={success}, 结果={result}")

async def test_max_retries():
    """测试最大重试次数限制"""
    print("\n=== 测试最大重试次数限制 ===")
    
    test_content = "测试内容"
    
    # 模拟始终返回错误JSON的响应
    def mock_post_always_bad(*args, **kwargs):
        mock_response = MockResponse(200, {
            'choices': [{
                'message': {
                    'content': "这始终是一个无效的JSON响应"
                }
            }]
        })
        return mock_response
    
    with patch('httpx.AsyncClient') as mock_client:
        mock_instance = AsyncMock()
        mock_instance.post = AsyncMock(side_effect=mock_post_always_bad)
        mock_client.return_value.__aenter__.return_value = mock_instance
        
        result = await generate_llm_response(test_content, "summary")
        
        print(f"最大重试后的结果: {result}")
        
        # 检查是否返回了错误响应
        if result and "JSON解析失败次数过多" in str(result.values()):
            print("✓ 最大重试次数限制测试通过")
        else:
            print("✗ 最大重试次数限制测试失败")

async def main():
    """主测试函数"""
    print("开始测试JSON解析失败重试逻辑...")
    
    await test_parse_function_directly()
    await test_json_retry_logic()
    await test_max_retries()
    
    print("\n所有测试完成！")
    
    print("\n功能说明:")
    print("1. 当JSON解析失败时，系统会重新请求LLM生成内容")
    print("2. 最多重试5次（MAX_RETRIES配置）")
    print("3. 如果重试次数用完仍然失败，返回错误响应")
    print("4. API请求失败和JSON解析失败是独立的重试计数")

if __name__ == "__main__":
    asyncio.run(main())
