#!/usr/bin/env python
"""
摘要生成器运行脚本

此脚本提供了一个命令行界面，用于运行摘要生成器，
可以选择运行完整处理或测试模式。
"""

import argparse
import asyncio
import os
import sys


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="通用文本处理器 - 支持摘要生成和问题生成")
    parser.add_argument(
        "--test",
        action="store_true",
        help="运行测试模式，只处理少量文件"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=3,
        help="测试模式下处理的文件数量限制（默认：3）"
    )
    parser.add_argument(
        "--source",
        type=str,
        default="structured_result",
        help="源数据目录路径（默认：structured_result）"
    )
    parser.add_argument(
        "--target",
        type=str,
        default="structured_result_summary",
        help="目标输出目录路径（默认：structured_result_summary）"
    )
    parser.add_argument(
        "--concurrent",
        type=int,
        default=10,
        help="最大并发请求数（默认：10）"
    )
    parser.add_argument(
        "--task-type",
        type=str,
        choices=["summary", "question"],
        default="question",
        help="任务类型：summary（摘要生成）或 question（问题生成）（默认：question）"
    )
    return parser.parse_args()


async def main():
    """
    主函数
    """
    args = parse_args()

    # 导入模块
    import summary_generator
    import test_summary_generator

    # 更新配置
    config_changed = False
    if args.source != "structured_result":
        summary_generator.SOURCE_DIR = args.source
        config_changed = True
    if args.target != "structured_result_summary":
        summary_generator.TARGET_DIR = args.target
        config_changed = True
    if args.concurrent != 10:
        summary_generator.MAX_CONCURRENT_REQUESTS = args.concurrent
        config_changed = True
    if args.task_type != "question":
        summary_generator.TASK_TYPE = args.task_type
        config_changed = True

    if config_changed:
        print(f"更新配置：源目录={args.source}, 目标目录={args.target}, 并发数={args.concurrent}, 任务类型={args.task_type}")

    # 确保目录结构正确创建
    print("确保目录结构完整...")
    await summary_generator.create_directory_structure()

    if args.test:
        print(f"运行测试模式，处理最多 {args.limit} 个文件...")
        print(f"任务类型: {args.task_type}")
        # 运行测试脚本，传递文件限制参数和任务类型
        await test_summary_generator.test_generation(file_limit=args.limit, task_type=args.task_type)
    else:
        print("运行完整处理模式...")
        print(f"任务类型: {args.task_type}")
        # 运行完整处理脚本
        await summary_generator.main(task_type=args.task_type)


if __name__ == "__main__":
    asyncio.run(main())