#!/usr/bin/env python
"""
真实场景测试脚本

这个脚本使用真实的文档内容测试JSON解析重试功能。
"""

import asyncio
import os
from summary_generator import generate_llm_response, read_txt_file

async def test_real_document():
    """测试真实文档的处理"""
    print("=== 真实文档处理测试 ===")
    
    # 查找测试文件
    source_dir = "757931849526939648_SJ-20240307162300-010-ZXR10 M6000-S（V5.00.10.86）电信级路由器 配置指导"
    
    if not os.path.exists(source_dir):
        print(f"源目录不存在: {source_dir}")
        print("请确保源目录存在并包含txt文件")
        return
    
    # 获取第一个txt文件进行测试
    test_file = None
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            if file.endswith(".txt"):
                test_file = os.path.join(root, file)
                break
        if test_file:
            break
    
    if not test_file:
        print(f"在目录 {source_dir} 中未找到txt文件")
        return
    
    print(f"测试文件: {test_file}")
    
    # 读取文件内容
    content = await read_txt_file(test_file)
    if not content:
        print("文件内容为空")
        return
    
    print(f"文件内容长度: {len(content)} 字符")
    print(f"内容预览: {content[:200]}...")
    
    # 测试摘要生成
    print("\n--- 测试摘要生成 ---")
    try:
        summary_result = await generate_llm_response(content, "summary")
        if summary_result:
            print("✓ 摘要生成成功")
            print(f"结果键: {list(summary_result.keys())}")
            if 'DA' in summary_result:
                print(f"描述性摘要: {summary_result['DA'][:100]}...")
        else:
            print("✗ 摘要生成失败")
    except Exception as e:
        print(f"✗ 摘要生成出错: {e}")
    
    # 测试问题生成
    print("\n--- 测试问题生成 ---")
    try:
        question_result = await generate_llm_response(content, "question")
        if question_result:
            print("✓ 问题生成成功")
            print(f"结果键: {list(question_result.keys())}")
            for category, questions in question_result.items():
                if isinstance(questions, list) and questions:
                    print(f"{category}: {len(questions)} 个问题")
                    print(f"  示例: {questions[0]}")
        else:
            print("✗ 问题生成失败")
    except Exception as e:
        print(f"✗ 问题生成出错: {e}")

async def test_small_content():
    """测试小内容的处理"""
    print("\n=== 小内容处理测试 ===")
    
    test_content = """
    vLLM是一个高性能的大语言模型推理库，专为生产环境设计。
    它提供了以下主要功能：
    1. 高吞吐量的推理服务
    2. 支持多种模型架构
    3. 易于使用的API接口
    4. 分页注意力机制优化内存使用
    
    安装方法：
    pip install vllm
    
    基本使用：
    from vllm import LLM
    llm = LLM(model="facebook/opt-125m")
    outputs = llm.generate(["Hello, my name is"], sampling_params=SamplingParams(temperature=0.8, top_p=0.95))
    """
    
    print(f"测试内容长度: {len(test_content)} 字符")
    
    # 测试摘要生成
    print("\n--- 小内容摘要生成 ---")
    try:
        summary_result = await generate_llm_response(test_content, "summary")
        if summary_result:
            print("✓ 摘要生成成功")
            for key, value in summary_result.items():
                print(f"{key}: {value}")
        else:
            print("✗ 摘要生成失败")
    except Exception as e:
        print(f"✗ 摘要生成出错: {e}")
    
    # 测试问题生成
    print("\n--- 小内容问题生成 ---")
    try:
        question_result = await generate_llm_response(test_content, "question")
        if question_result:
            print("✓ 问题生成成功")
            for category, questions in question_result.items():
                if isinstance(questions, list) and questions:
                    print(f"\n{category}:")
                    for i, question in enumerate(questions, 1):
                        print(f"  {i}. {question}")
        else:
            print("✗ 问题生成失败")
    except Exception as e:
        print(f"✗ 问题生成出错: {e}")

async def main():
    """主测试函数"""
    print("开始真实场景测试...")
    print("这个测试将使用实际的LLM API，可能需要一些时间")
    print("=" * 60)
    
    # 测试小内容（更容易成功）
    await test_small_content()
    
    # 测试真实文档（如果存在）
    await test_real_document()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    
    print("\n重试功能说明:")
    print("1. 如果看到 'JSON解析失败，重新请求LLM生成' 的日志，说明重试功能正在工作")
    print("2. 如果最终成功生成结果，说明重试功能有效")
    print("3. 如果重试5次后仍然失败，会返回错误响应")
    print("4. 可以通过日志观察重试过程")

if __name__ == "__main__":
    asyncio.run(main())
