#!/usr/bin/env python3
"""
LangExtract 真实Demo - 使用私域OpenAI格式模型

这个demo展示了如何正确使用langextract库配合私域OpenAI格式的API进行文本信息提取。
这次我们使用真正的langextract功能，而不是模仿其格式。

API地址: http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions
模型: deepseek_v3_int8_vpc
"""

import textwrap
from typing import List
import langextract as lx
from langextract_private_provider import PrivateOpenAIProvider, ensure_provider_registered


class LangExtractRealDemo:
    """
    真正的LangExtract演示类
    使用langextract的实际功能进行信息提取
    """
    
    def __init__(self):
        """初始化demo"""
        # 确保我们的自定义provider已注册
        ensure_provider_registered()
        
        # 配置私域API参数
        self.api_key = "618149eb-d43e-4ddc-b406-b0c0e1efd281"
        self.base_url = "http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions"
        self.model_id = "deepseek_v3_int8_vpc"
    
    def demo_person_extraction(self):
        """演示人员信息提取"""
        print("\n" + "="*60)
        print("🧑 演示1: 人员信息提取")
        print("="*60)
        
        # 测试文本
        text = """
        张三是我们公司的高级软件工程师，今年32岁，负责后端开发工作。
        他的邮箱是********************，手机号码是13812345678。
        李四是前端开发工程师，25岁，专门负责React开发，联系方式是****************。
        王五是项目经理，38岁，有10年项目管理经验，电话是13987654321。
        """
        
        # 定义提取示例
        examples = [
            lx.data.ExampleData(
                text="小明是软件工程师，今年28岁，邮箱是*****************",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="person",
                        extraction_text="小明是软件工程师，今年28岁",
                        attributes={
                            "name": "小明",
                            "age": "28",
                            "job": "软件工程师",
                            "email": "<EMAIL>"
                        }
                    )
                ]
            )
        ]
        
        try:
            # 创建模型配置，明确指定我们的私域provider
            config = lx.factory.ModelConfig(
                model_id=self.model_id,
                provider="PrivateOpenAIProvider",
                provider_kwargs={
                    "api_key": self.api_key,
                    "base_url": self.base_url
                }
            )
            model = lx.factory.create_model(config)

            # 使用langextract进行提取
            result = lx.extract(
                text_or_documents=text,
                model=model,  # 传递配置好的模型
                prompt_description="从文本中提取人员信息，包括姓名、年龄、职位、联系方式等",
                examples=examples,
                temperature=0.3
            )
            
            print(f"✅ 提取成功！找到 {len(result.extractions)} 个人员信息:")
            for i, extraction in enumerate(result.extractions, 1):
                print(f"\n{i}. 类别: {extraction.extraction_class}")
                print(f"   文本: {extraction.extraction_text}")
                if extraction.attributes:
                    print("   属性:")
                    for key, value in extraction.attributes.items():
                        print(f"     {key}: {value}")
                        
        except Exception as e:
            print(f"❌ 提取失败: {e}")
    
    def demo_bgp_extraction(self):
        """演示BGP配置信息提取"""
        print("\n" + "="*60)
        print("🌐 演示2: BGP网络配置提取")
        print("="*60)
        
        # BGP配置文本
        text = """
        router bgp 65001
         bgp router-id ***********
         neighbor ******** remote-as 65002
         neighbor ******** description "ISP Connection"
         neighbor *********** remote-as 65001
         neighbor *********** next-hop-self
         network *********** mask ***********
         network ******** mask ***********
        """
        
        # 定义BGP提取示例
        examples = [
            lx.data.ExampleData(
                text="router bgp 64512\n bgp router-id ********\n neighbor ******** remote-as 64513",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="bgp_config",
                        extraction_text="router bgp 64512",
                        attributes={
                            "as_number": "64512",
                            "router_id": "********"
                        }
                    ),
                    lx.data.Extraction(
                        extraction_class="bgp_neighbor",
                        extraction_text="neighbor ******** remote-as 64513",
                        attributes={
                            "neighbor_ip": "********",
                            "remote_as": "64513"
                        }
                    )
                ]
            )
        ]
        
        try:
            # 创建模型配置
            config = lx.factory.ModelConfig(
                model_id=self.model_id,
                provider="PrivateOpenAIProvider",
                provider_kwargs={
                    "api_key": self.api_key,
                    "base_url": self.base_url
                }
            )
            model = lx.factory.create_model(config)

            # 使用langextract进行BGP配置提取
            result = lx.extract(
                text_or_documents=text,
                model=model,
                prompt_description="从BGP配置中提取AS号、路由器ID、邻居配置等网络信息",
                examples=examples,
                temperature=0.1
            )
            
            print(f"✅ BGP配置提取成功！找到 {len(result.extractions)} 个配置项:")
            for i, extraction in enumerate(result.extractions, 1):
                print(f"\n{i}. 类别: {extraction.extraction_class}")
                print(f"   文本: {extraction.extraction_text}")
                if extraction.attributes:
                    print("   属性:")
                    for key, value in extraction.attributes.items():
                        print(f"     {key}: {value}")
                        
        except Exception as e:
            print(f"❌ BGP配置提取失败: {e}")
    
    def demo_tech_stack_extraction(self):
        """演示技术栈信息提取"""
        print("\n" + "="*60)
        print("💻 演示3: 技术栈信息提取")
        print("="*60)
        
        # 技术栈描述文本
        text = """
        我们的项目使用React 18.2作为前端框架，配合TypeScript 4.9进行开发。
        后端采用Node.js 18.x和Express 4.18框架，数据库使用PostgreSQL 14。
        部署环境是Docker容器，运行在Kubernetes 1.25集群上。
        CI/CD使用GitHub Actions，代码质量检查用ESLint和Prettier。
        """
        
        # 定义技术栈提取示例
        examples = [
            lx.data.ExampleData(
                text="项目使用Vue 3.0和Python Django 4.0，部署在AWS上",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="frontend_tech",
                        extraction_text="Vue 3.0",
                        attributes={
                            "technology": "Vue",
                            "version": "3.0",
                            "category": "frontend"
                        }
                    ),
                    lx.data.Extraction(
                        extraction_class="backend_tech",
                        extraction_text="Python Django 4.0",
                        attributes={
                            "technology": "Django",
                            "version": "4.0",
                            "language": "Python",
                            "category": "backend"
                        }
                    ),
                    lx.data.Extraction(
                        extraction_class="deployment",
                        extraction_text="部署在AWS上",
                        attributes={
                            "platform": "AWS",
                            "category": "deployment"
                        }
                    )
                ]
            )
        ]
        
        try:
            # 创建模型配置
            config = lx.factory.ModelConfig(
                model_id=self.model_id,
                provider="PrivateOpenAIProvider",
                provider_kwargs={
                    "api_key": self.api_key,
                    "base_url": self.base_url
                }
            )
            model = lx.factory.create_model(config)

            # 使用langextract进行技术栈提取
            result = lx.extract(
                text_or_documents=text,
                model=model,
                prompt_description="从项目描述中提取技术栈信息，包括前端、后端、数据库、部署等技术",
                examples=examples,
                temperature=0.2
            )
            
            print(f"✅ 技术栈提取成功！找到 {len(result.extractions)} 个技术组件:")
            for i, extraction in enumerate(result.extractions, 1):
                print(f"\n{i}. 类别: {extraction.extraction_class}")
                print(f"   文本: {extraction.extraction_text}")
                if extraction.attributes:
                    print("   属性:")
                    for key, value in extraction.attributes.items():
                        print(f"     {key}: {value}")
                        
        except Exception as e:
            print(f"❌ 技术栈提取失败: {e}")
    
    def run_all_demos(self):
        """运行所有演示"""
        print("🚀 LangExtract 私域API演示开始")
        print("使用真正的langextract功能进行信息提取")
        
        # 运行所有演示
        self.demo_person_extraction()
        self.demo_bgp_extraction()
        self.demo_tech_stack_extraction()
        
        print("\n" + "="*60)
        print("✨ 所有演示完成！")
        print("="*60)
        print("\n📝 总结:")
        print("- 使用了真正的langextract.extract()函数")
        print("- 集成了自定义的私域OpenAI Provider")
        print("- 展示了结构化信息提取的能力")
        print("- 支持few-shot学习和属性提取")


def main():
    """主函数"""
    demo = LangExtractRealDemo()
    demo.run_all_demos()


if __name__ == "__main__":
    main()
