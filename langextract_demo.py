#!/usr/bin/env python3
"""
LangExtract Demo - 使用私域OpenAI格式模型

这个demo展示了如何使用langextract库配合私域OpenAI格式的API进行文本信息提取。
API地址: http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions
模型: deepseek_v3_int8_vpc
"""

import asyncio
import json
import textwrap
from typing import Dict, List, Optional
import langextract as lx
import httpx


class PrivateOpenAIProvider:
    """
    私域OpenAI API提供者
    用于连接私域OpenAI格式的API
    """

    def __init__(
        self,
        api_key: str,
        base_url: str,
        model_name: str = "deepseek_v3_int8_vpc",
        temperature: float = 0.5,
        max_tokens: int = 4000
    ):
        """
        初始化私域OpenAI提供者

        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens

    async def generate_response(self, messages: List[Dict[str, str]]) -> str:
        """
        发送请求到私域API并获取响应

        Args:
            messages: 消息列表

        Returns:
            API响应内容
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        payload = {
            "model": self.model_name,
            "messages": messages,
            "stream": False,  # 为了简化处理，这里不使用流式响应
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    self.base_url,
                    headers=headers,
                    json=payload,
                    timeout=60.0
                )
                response.raise_for_status()

                result = response.json()

                # 解析OpenAI格式的响应
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    raise ValueError(f"Unexpected response format: {result}")

            except httpx.HTTPError as e:
                raise Exception(f"HTTP error occurred: {e}")
            except json.JSONDecodeError as e:
                raise Exception(f"Failed to parse JSON response: {e}")


class LangExtractDemo:
    """
    LangExtract演示类
    """

    def __init__(self):
        """初始化demo"""
        # 配置私域API参数
        self.api_key = "618149eb-d43e-4ddc-b406-b0c0e1efd281"
        self.base_url = "http://0.0.0.0:8007/CUCCAI-llm-hub/chat/completions"
        self.model_name = "deepseek_v3_int8_vpc"

        # 创建私域API提供者
        self.provider = PrivateOpenAIProvider(
            api_key=self.api_key,
            base_url=self.base_url,
            model_name=self.model_name,
            temperature=0.5,
            max_tokens=4000
        )

    async def extract_with_custom_api(self, text: str, prompt: str, examples: Optional[List] = None) -> Dict:
        """
        使用自定义API进行信息提取

        Args:
            text: 要提取信息的文本
            prompt: 提取任务的提示词
            examples: 示例数据（可选）

        Returns:
            提取结果字典
        """
        # 构建提示词
        system_prompt = f"""你是一个专业的信息提取助手。{prompt}

请按照以下JSON格式返回提取结果：
{{
    "extractions": [
        {{
            "text": "提取的文本片段",
            "type": "信息类型",
            "attributes": {{
                "key": "value"
            }}
        }}
    ]
}}

请确保返回有效的JSON格式。"""

        user_prompt = f"请从以下文本中提取信息：\n\n{text}"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            response = await self.provider.generate_response(messages)

            # 尝试解析JSON响应
            try:
                # 清理响应文本，移除可能的markdown标记
                cleaned_response = response.strip()
                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith("```"):
                    cleaned_response = cleaned_response[:-3]
                cleaned_response = cleaned_response.strip()

                result = json.loads(cleaned_response)
                return result
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回原始响应
                return {"raw_response": response, "error": "Failed to parse JSON"}

        except Exception as e:
            return {"error": str(e)}

    async def demo_basic_extraction(self):
        """
        基础信息提取演示
        """
        print("=== 基础信息提取演示 ===")

        # 示例文本
        text = """
        张三是一名软件工程师，今年28岁，住在北京市朝阳区。
        他在阿里巴巴公司工作，月薪25000元。
        他的邮箱是********************，手机号是13812345678。
        他喜欢编程和阅读，业余时间经常参加技术meetup。
        """

        # 定义提取任务的提示词
        prompt = textwrap.dedent("""\
            从文本中提取人员的基本信息，包括姓名、年龄、居住地、工作信息、联系方式和兴趣爱好。
            请确保提取的信息准确，并且直接来源于原文。
            """)

        # 提供示例来指导模型
        examples = [
            lx.data.ExampleData(
                text="李四是一名产品经理，今年30岁，住在上海市浦东新区。他在腾讯公司工作，月薪30000元。",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="person_info",
                        extraction_text="李四",
                        attributes={"type": "姓名"}
                    ),
                    lx.data.Extraction(
                        extraction_class="person_info",
                        extraction_text="30岁",
                        attributes={"type": "年龄"}
                    ),
                    lx.data.Extraction(
                        extraction_class="person_info",
                        extraction_text="上海市浦东新区",
                        attributes={"type": "居住地"}
                    ),
                    lx.data.Extraction(
                        extraction_class="person_info",
                        extraction_text="腾讯公司",
                        attributes={"type": "工作公司"}
                    ),
                    lx.data.Extraction(
                        extraction_class="person_info",
                        extraction_text="月薪30000元",
                        attributes={"type": "薪资"}
                    )
                ]
            )
        ]

        try:
            # 使用自定义的API调用方式
            result = await self.extract_with_custom_api(text, prompt, examples)
            print("提取结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
        except Exception as e:
            print(f"提取失败: {e}")
            return None

    async def demo_network_config_extraction(self):
        """
        网络配置信息提取演示（BGP相关）
        """
        print("\n=== 网络配置信息提取演示 ===")

        # BGP配置示例文本
        text = """
        BGP配置示例：

        router bgp 65001
         bgp router-id ***********
         neighbor ******** remote-as 65002
         neighbor ******** description "ISP-A"
         neighbor ******** password cisco123
         neighbor ********** remote-as 65001
         neighbor ********** description "Internal-Peer"
         neighbor ********** next-hop-self

         address-family ipv4 unicast
          network *********** mask ***********
          network ********* mask ***********
          neighbor ******** activate
          neighbor ********** activate
         exit-address-family
        """

        # 定义BGP配置提取任务的提示词
        prompt = textwrap.dedent("""\
            从BGP配置文本中提取关键配置信息，包括：
            1. 本地AS号码
            2. BGP路由器ID
            3. 邻居配置信息（IP地址、远程AS号、描述、密码等）
            4. 网络宣告信息（网络地址和子网掩码）

            请确保提取的信息准确，并且直接来源于配置文本。
            """)

        try:
            result = await self.extract_with_custom_api(text, prompt)
            print("BGP配置提取结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
        except Exception as e:
            print(f"BGP配置提取失败: {e}")
            return None

    async def demo_structured_data_extraction(self):
        """
        结构化数据提取演示
        """
        print("\n=== 结构化数据提取演示 ===")

        # 复杂的技术文档示例
        text = """
        系统架构文档

        前端技术栈：
        - React 18.2.0
        - TypeScript 4.9.5
        - Ant Design 5.1.2
        - Webpack 5.75.0

        后端技术栈：
        - Spring Boot 2.7.8
        - Java 17
        - MySQL 8.0.32
        - Redis 7.0.8

        部署环境：
        - 生产环境：阿里云ECS，4核8G，CentOS 7.9
        - 测试环境：腾讯云CVM，2核4G，Ubuntu 20.04
        - 开发环境：本地Docker，Docker Desktop 4.16.2

        性能指标：
        - QPS: 1000
        - 响应时间: 平均200ms，P99 500ms
        - 可用性: 99.9%
        """

        # 定义技术架构提取任务的提示词
        prompt = textwrap.dedent("""\
            从技术架构文档中提取结构化信息，包括：
            1. 前端技术栈（技术名称和版本号）
            2. 后端技术栈（技术名称和版本号）
            3. 部署环境信息（环境类型、云服务商、配置规格、操作系统）
            4. 性能指标（QPS、响应时间、可用性等）

            请将提取的信息按照技术栈、环境、性能等类别进行分组。
            """)

        try:
            result = await self.extract_with_custom_api(text, prompt)
            print("技术架构提取结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
        except Exception as e:
            print(f"技术架构提取失败: {e}")
            return None

    async def run_all_demos(self):
        """
        运行所有演示
        """
        print("开始运行LangExtract演示...")
        print(f"使用模型: {self.model_name}")
        print(f"API地址: {self.base_url}")
        print("-" * 50)

        # 运行各个演示
        await self.demo_basic_extraction()
        await self.demo_network_config_extraction()
        await self.demo_structured_data_extraction()

        print("\n" + "=" * 50)
        print("所有演示完成！")


async def main():
    """
    主函数
    """
    demo = LangExtractDemo()
    await demo.run_all_demos()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())